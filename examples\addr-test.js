const ODudeSDK = require('../src/index');

const wallet_address = '******************************************'; // Replace with your wallet address

async function main() {

    // Initialize SDK
    const sdk = new ODudeSDK({
        rpcUrl_sepolia: process.env.BASE_SEPOLIA_RPC_URL || 'https://sepolia.base.org'
      });
      try {
        sdk.connectNetwork('basesepolia');
        console.log('✓ Connected to Base Sepolia network\n');
      } catch (error) {
        console.log('❌ Failed to connect to Base Sepolia:', error.message);
        return;
      }


       const totalNames = await sdk.getTotalNames(wallet_address);
    console.log('Total names owned:', totalNames.toString());
    


}
// Handle both direct execution and hardhat run
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Error:', error);
      process.exit(1);
    });
} else {
  module.exports = main;
}