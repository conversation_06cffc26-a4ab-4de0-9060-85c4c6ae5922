/**
 * Helper utilities for ODude SDK
 */

/**
 * Normalize ODude name to lowercase
 * @param {string} name - The ODude name
 * @returns {string} Normalized name
 */
function normalizeName(name) {
  if (!name || typeof name !== 'string') {
    throw new Error('Invalid name: must be a non-empty string');
  }
  return name.toLowerCase().trim();
}

/**
 * Extract TLD from a full ODude name
 * @param {string} name - Full ODude name (e.g., "alice@crypto" or "bob@alice@crypto")
 * @returns {string} The TLD portion
 */
function extractTLD(name) {
  const normalized = normalizeName(name);
  const parts = normalized.split('@');
  if (parts.length === 0) {
    throw new Error('Invalid name format');
  }
  return parts[parts.length - 1];
}

/**
 * Extract subdomain from a full ODude name
 * @param {string} name - Full ODude name (e.g., "alice.odude")
 * @returns {string|null} The subdomain portion or null if it's a TLD
 */
function extractSubdomain(name) {
  const normalized = normalizeName(name);
  const parts = normalized.split('.');
  if (parts.length <= 1) {
    return null;
  }
  return parts.slice(0, -1).join('.');
}

/**
 * Check if a name is a TLD (single part)
 * @param {string} name - The ODude name
 * @returns {boolean} True if it's a TLD
 */
function isTLD(name) {
  const normalized = normalizeName(name);
  return !normalized.includes('.');
}

/**
 * Check if a name is a subdomain (multiple parts)
 * @param {string} name - The ODude name
 * @returns {boolean} True if it's a subdomain
 */
function isSubdomain(name) {
  return !isTLD(name);
}

/**
 * Parse a full ODude name into components
 * @param {string} name - Full ODude name
 * @returns {Object} Object with tld, subdomain, and parts
 */
function parseName(name) {
  const normalized = normalizeName(name);
  const parts = normalized.split('@');
  
  return {
    full: normalized,
    tld: parts[parts.length - 1],
    subdomain: parts.length > 1 ? parts.slice(0, -1).join('@') : null,
    parts: parts,
    isTLD: parts.length === 1,
    isSubdomain: parts.length > 1
  };
}

/**
 * Validate Ethereum address
 * @param {string} address - Ethereum address
 * @returns {boolean} True if valid
 */
function isValidAddress(address) {
  return /^0x[a-fA-F0-9]{40}$/.test(address);
}

/**
 * Format token ID to string
 * @param {*} tokenId - Token ID (can be BigInt, number, or string)
 * @returns {string} Formatted token ID
 */
function formatTokenId(tokenId) {
  if (typeof tokenId === 'bigint') {
    return tokenId.toString();
  }
  if (typeof tokenId === 'number') {
    return tokenId.toString();
  }
  return String(tokenId);
}

/**
 * Format ether value to wei
 * @param {string|number} ether - Ether value
 * @returns {bigint} Wei value
 */
function parseEther(ether) {
  const { parseEther: ethersParseEther } = require('ethers');
  return ethersParseEther(String(ether));
}

/**
 * Format wei value to ether
 * @param {bigint|string} wei - Wei value
 * @returns {string} Ether value
 */
function formatEther(wei) {
  const { formatEther: ethersFormatEther } = require('ethers');
  return ethersFormatEther(wei);
}

module.exports = {
  normalizeName,
  extractTLD,
  extractSubdomain,
  isTLD,
  isSubdomain,
  parseName,
  isValidAddress,
  formatTokenId,
  parseEther,
  formatEther
};

