/**
 * Airdrop Check Example
 * Demonstrates how to check airdrop eligibility and claim status
 */

const ODudeSDK = require('../src/index');

async function main() {
  console.log('=== ODude Airdrop Check Example ===\n');

  // Initialize SDK with Base Sepolia (the working network)
  const sdk = new ODudeSDK({
    rpcUrl_sepolia: process.env.BASE_SEPOLIA_RPC_URL || 'https://sepolia.base.org',
    rpcUrl: process.env.LOCALHOST_RPC_URL || 'http://127.0.0.1:8545'
  });

  try {
    sdk.connectNetwork('basesepolia');
    console.log('✓ Connected to Base Sepolia network\n');
  } catch (error) {
    console.log('❌ Failed to connect to Base Sepolia:', error.message);
    return;
  }

  // Test addresses (Hardhat default accounts)
  const testAddresses = [
    '******************************************',
    '******************************************',
    '******************************************'
  ];

  console.log('--- Airdrop Status ---');
  try {
    // Check airdrop count for 'crypto' TLD
    const airdropCount = await sdk.rwairdrop().getTLDAirdropCount('crypto');
    console.log('Airdrop count for "crypto" TLD:', airdropCount.toString());

    if (airdropCount > 0) {
      // Get airdrop IDs for the TLD
      const airdropIds = await sdk.rwairdrop().getTLDAirdropIds('crypto');
      console.log('Airdrop IDs:', airdropIds.map(id => id.toString()));

      // Check first airdrop details
      const airdropInfo = await sdk.rwairdrop().getAirdropInfoByTLD('crypto', 0);
      console.log('First airdrop info:', {
        tokenAddress: airdropInfo.tokenAddress,
        totalAmount: sdk.utils.formatEther(airdropInfo.totalAmount),
        perUserShare: sdk.utils.formatEther(airdropInfo.perUserShare),
        remainingBalance: sdk.utils.formatEther(airdropInfo.remainingBalance),
        isActive: airdropInfo.isActive,
        isWithdrawn: airdropInfo.isWithdrawn
      });
    } else {
      console.log('No airdrops found for "crypto" TLD');
    }
  } catch (error) {
    console.log('Failed to get airdrop status:', error.message);
  }

  console.log('\n--- Checking Addresses ---');
  
  for (const address of testAddresses) {
    console.log(`\nAddress: ${address}`);
    
    try {
      // Get claimable airdrops for this user
      const claimableAirdrops = await sdk.rwairdrop().getClaimableAirdrops(address);
      console.log(`  Claimable airdrops: ${claimableAirdrops.length}`);

      if (claimableAirdrops.length > 0) {
        claimableAirdrops.forEach((airdrop, index) => {
          console.log(`  Airdrop ${index + 1}:`);
          console.log(`    TLD: ${airdrop.tldName}`);
          console.log(`    Airdrop ID: ${airdrop.airdropId.toString()}`);
          console.log(`    Token: ${airdrop.tokenAddress}`);
          console.log(`    Per User Share: ${sdk.utils.formatEther(airdrop.perUserShare)}`);
          console.log(`    Can Claim: ${airdrop.canClaim}`);
        });
      } else {
        console.log('  No claimable airdrops found');
      }

      // Check user domains in 'crypto' TLD
      const userDomains = await sdk.rwairdrop().getUserDomainsInTLD(address, 'crypto');
      console.log(`  Domains in 'crypto' TLD: ${userDomains.length}`);
      if (userDomains.length > 0) {
        console.log(`    Domains: ${userDomains.join(', ')}`);
      }
    } catch (error) {
      console.log('  Error:', error.message);
    }
  }

  console.log('\n--- Individual Checks ---');

  const checkAddress = testAddresses[0];
  console.log(`\nDetailed check for: ${checkAddress}`);

  try {
    // Check if user has any domains that have claimed from airdrop 0 in 'crypto' TLD
    const userDomains = await sdk.rwairdrop().getUserDomainsInTLD(checkAddress, 'crypto');

    if (userDomains.length > 0) {
      console.log(`User has ${userDomains.length} domains in 'crypto' TLD:`);

      for (const domain of userDomains) {
        const hasClaimed = await sdk.rwairdrop().hasDomainClaimed(domain, 0);
        const claimedAmount = await sdk.rwairdrop().getDomainClaimedAmount(domain, 0);

        console.log(`  Domain: ${domain}`);
        console.log(`    Has claimed from airdrop 0: ${hasClaimed}`);
        console.log(`    Claimed amount: ${sdk.utils.formatEther(claimedAmount)}`);
      }

      console.log('\n💰 To claim from an airdrop, use:');
      console.log('  const tx = await sdk.rwairdrop().claimShare("crypto", 0, "your-domain");');
      console.log('  await tx.wait();');
    } else {
      console.log('User has no domains in "crypto" TLD');
    }
  } catch (error) {
    console.log('Error during detailed check:', error.message);
  }

  console.log('\n✓ Example completed!');
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Error:', error);
    process.exit(1);
  });

