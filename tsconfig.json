{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "declaration": true, "declarationMap": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo"}, "include": ["src/**/*", "abi/**/*.json", "config/**/*.json"], "exclude": ["node_modules", "dist", "test", "examples", "scripts"]}